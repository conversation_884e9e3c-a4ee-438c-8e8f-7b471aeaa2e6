<script lang="ts" setup>
import { nextTick,} from 'vue'
import { formSchema } from './pointsSettings.data'
import { BasicForm, useForm } from '@/components/Form'
import { BasicModal, useModalInner } from '@/components/Modal'
import { useMessage } from '@/hooks/web/useMessage'
import {getPointConfigDetail, updatePointConfig } from '@/api/mall/pointsMall/pointsSettings'

const emit = defineEmits(['success', 'register'])
const { createMessage } = useMessage()
const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
  labelWidth: 120,
  baseColProps: { span: 24 },
  schemas: formSchema,
  showActionButtonGroup: false,
  actionColOptions: { span: 23 },
})
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  resetFields()
  setModalProps({ confirmLoading: false })
  const res = await getPointConfigDetail({ id: data.id })
  console.log(res);
  setFieldsValue({ ...res })
  await nextTick()
})

async function handleSubmit() {
  try {
    const values = await validate()
    setModalProps({ confirmLoading: true })
    await updatePointConfig(values)
    closeModal()
    emit('success')
    createMessage.success('操作成功')
  }
  finally {
    setModalProps({ confirmLoading: false })
  }
}
</script>
<template>
  <BasicModal v-bind="$attrs" title="积分配置详情" @register="registerModal" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
