import type { BasicColumn, FormSchema } from '@/components/Table'
import { useRender } from '@/components/Table'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'


export const columns: BasicColumn[] = [
  // ==================== 核心信息 ====================
  {
    title: '编号',
    dataIndex: 'id',
    width: 160,
    defaultHidden: true, // 默认隐藏
  },
  {
    title: '订单编号',
    dataIndex: 'orderNo',
    width: 180,
  },
  {
    title: '用户信息',
    dataIndex: 'userInfo',
    width: 200,
    customRender: ({ record }) => {
      const { nickname, mobile, memberNo } = record
      return h('div', null, [
        h('p', { class: 'm-0' }, [
          h('p', null, `昵称: ${nickname || '-'}`),
        ]),
        h('p', { class: 'm-0 text-gray-500' }, `手机号: ${mobile || '-'}`),
        h('p', { class: 'm-0 text-gray-500' }, `CLUBID: ${memberNo || '-'}`),
      ])
    },
  },
  {
    title: '真实姓名',
    dataIndex: 'realName',
    width: 120,
    defaultHidden: true, // 默认隐藏
  },
  {
    title: '会员ID',
    dataIndex: 'memberId',
    width: 120,
    defaultHidden: true, // 默认隐藏
  },
  // ==================== 收货与物流 ====================
  {
    title: '收货信息',
    dataIndex: 'shippingInfo',
    width: 250,
    customRender: ({ record }) => {
      const { consignee, phone, province, city, district, address } = record
      const region = [province, city, district].filter(Boolean).join('/')
      return h('div', null, [
        h('p', { class: 'm-0' }, [
          h('strong', null, `${consignee} `),
          `(${phone})`,
        ]),
        h('p', { class: 'm-0 text-gray-500' }, region),
        h('p', { class: 'm-0 text-gray-500' }, address),
      ])
    },
  },
  // ==================== 订单与支付详情 ====================
  {
    title: '订单信息',
    dataIndex: 'orderPaymentInfo', // 虚拟字段
    width: 260,
    customRender: ({ record }) => {
      const { orderPoints, totalAmount, payAmount, postage } = record
      const valueStyle = 'color: #FF0000; font-weight: bold'

      return h('div', null, [
        h('p', { class: 'm-0' }, [
          '消耗积分：',
          h('span', { style: valueStyle }, orderPoints ?? '-'),
        ]),
        h('p', { class: 'm-0 text-gray-500' }, [
          '订单金额：',
          h('span', { style: valueStyle }, totalAmount != null ? `${totalAmount}` : '-'),
        ]),
        h('p', { class: 'm-0 text-gray-500' }, [
          '邮费：',
          h('span', { style: valueStyle }, postage != null ? `${postage}` : '-'),
        ]),
        h('p', { class: 'm-0 text-gray-500' }, [
          '支付金额：',
          h('span', { style: valueStyle }, payAmount != null ? `${payAmount}` : '-'),
        ]),
      ])
    },
  },
  {
    title: '购买数量',
    dataIndex: 'orderQuantity',
    width: 120,
  },
  {
    title: '订单状态',
    dataIndex: 'orderStatus',
    width: 100,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.TRADE_ORDER_STATUS)
    },
  },
  {
    title: '支付状态',
    dataIndex: 'payStatus',
    width: 100,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.PAY_ORDER_STATUS)
    },
    defaultHidden: true, // 默认隐藏
  },
  {
    title: '退款状态',
    dataIndex: 'refundStatus',
    width: 100,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.POINT_REFUND_STATUS)
    },
    defaultHidden: true, // 默认隐藏
  },
  // ==================== 时间信息 ====================
  {
    title: '下单时间',
    dataIndex: 'orderTime',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text)
    },
  },
  {
    title: '物流信息',
    dataIndex: 'logisticsInfo',
    width: 250,
    customRender: ({ record }) => {
      const { deliveryTime, logisticsEnname, logisticsName, logisticsNum } = record
      if (!logisticsNum)
        return h('div', null, [
          h('p', '--'),
          h('p', '--'),
          h('p', '--'),
        ])

      return h('div', null, [
        h('p', { class: 'm-0' }, `${logisticsName || ''} (${logisticsEnname || ''})`),
        h('p', { class: 'm-0 text-gray-500' }, logisticsNum),
        h('p', { class: 'm-0 text-gray-500' }, `发货时间: ${deliveryTime}`),
      ])
    },
  },
  {
    title: '其他信息',
    dataIndex: 'payCancelInfo', // 虚拟字段
    width: 300,
    customRender: ({ record }) => {
      const { cancelTime, cancelReason, payTime } = record
      const lines: VNode[] = []

      if (cancelTime) {
        lines.push(
          h('p', { class: 'm-0 text-red-500' }, `取消：${useRender.renderDate(cancelTime)}`),
        )
      }else{
        lines.push(
          h('p', '--')
        )
      }

      if (cancelReason) {
        lines.push(
          h('p', { class: 'm-0 text-red-500' }, `原因：${cancelReason}`),
        )
      }else{
        lines.push(
          h('p', '--')
        )
      }

      if (payTime) {
        lines.push(
          h('p', { class: 'm-0 text-gray-500' }, `支付：${useRender.renderDate(payTime)}`),
        )
      }else{
        lines.push(
          h('p', '--')
        )
      }

      return h('div', null, lines)
    },
  },
  {
    title: '发货时间',
    dataIndex: 'deliveryTime',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text)
    },
    defaultHidden: true, // 默认隐藏
  },
  {
    title: '订单过期时间',
    dataIndex: 'expiredTime',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text)
    },
    defaultHidden: true, // 默认隐藏
  },
  // ==================== 其他信息 (默认隐藏) ====================
  {
    title: '邮寄类型',
    dataIndex: 'logisticsType',
    width: 120,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.POINT_POST)
    },
    defaultHidden: true,
  },
  {
    title: '是否虚拟',
    dataIndex: 'isVirtual',
    width: 100,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.POINT_VIRTUAL)
    },
    defaultHidden: true,
  },
  {
    title: '完成与关闭信息',
    dataIndex: 'completeCloseInfo', // 虚拟字段
    width: 300,
    customRender: ({ record }) => {
      const { completeTime, closedTime } = record
      const lines: VNode[] = []

      if (completeTime) {
        lines.push(
          h('p', { class: 'm-0 text-green-600' }, `完成时间：${useRender.renderDate(completeTime)}`),
        )
      }

      if (closedTime) {
        lines.push(
          h('p', { class: 'm-0 text-gray-500' }, `关闭时间：${useRender.renderDate(closedTime)}`),
        )
      }

      return h('div', null, lines.length > 0 ? lines : h('span', { class: 'text-gray-400' }, '无'))
    },
    defaultHidden: true, // 默认隐藏
  },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '订单编号',
    field: 'orderNo',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '用户昵称',
    field: 'nickname',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '手机号',
    field: 'phone',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '积分值',
    field: 'orderPoints',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '订单状态',
    field: 'orderStatus',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.TRADE_ORDER_STATUS, 'number'),
    },
    colProps: { span: 8 },
  },
  {
    label: '下单时间',
    field: 'orderTime',
    component: 'RangePicker',
    colProps: { span: 8 },
  },
  {
    label: '物流运单号',
    field: 'logisticsNum',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: 'CLUBID',
    field: 'memberNo',
    component: 'Input',
    colProps: { span: 8 },
  },
]

export const createFormSchema: FormSchema[] = [
  {
    label: '编号',
    field: 'id',
    show: false,
    component: 'Input',
  },
  {
    label: '订单编号',
    field: 'orderNo',
    required: true,
    component: 'Input',
  },
  {
    label: '会员ID',
    field: 'memberId',
    required: true,
    component: 'Input',
  },
  {
    label: '昵称',
    field: 'nickname',
    required: true,
    component: 'Input',
  },
  {
    label: '真实姓名',
    field: 'realName',
    component: 'Input',
  },
  {
    label: '收货人',
    field: 'consignee',
    component: 'Input',
  },
  {
    label: '收货人手机号码',
    field: 'phone',
    component: 'Input',
  },
  {
    label: '省份',
    field: 'province',
    component: 'Input',
  },
  {
    label: '省份编码',
    field: 'provinceCode',
    component: 'Input',
  },
  {
    label: '城市',
    field: 'city',
    component: 'Input',
  },
  {
    label: '城市编码',
    field: 'cityCode',
    component: 'Input',
  },
  {
    label: '区县',
    field: 'district',
    component: 'Input',
  },
  {
    label: '区县编码',
    field: 'districtCode',
    component: 'Input',
  },
  {
    label: '详细地址',
    field: 'address',
    component: 'Input',
  },
  {
    label: '订单商品数量',
    field: 'orderQuantity',
    required: true,
    component: 'Input',
  },
  {
    label: '支付积分值',
    field: 'orderPoints',
    required: true,
    component: 'Input',
  },
  {
    label: '订单状态',
    field: 'orderStatus',
    required: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.TRADE_ORDER_STATUS, 'number'),
    },
  },
  {
    label: '订单金额',
    field: 'totalAmount',
    required: true,
    component: 'Input',
  },
  {
    label: '支付金额',
    field: 'payAmount',
    required: true,
    component: 'Input',
  },
  {
    label: '支付状态',
    field: 'payStatus',
    required: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.PAY_ORDER_STATUS, 'number'),
    },
  },
  {
    label: '支付时间',
    field: 'payTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '退款状态',
    field: 'refundStatus',
    required: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.POINT_REFUND_STATUS, 'number'),
    },
  },
  {
    label: '下单时间',
    field: 'orderTime',
    required: true,
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '订单备注',
    field: 'remark',
    component: 'Input',
  },
  {
    label: '是否虚拟',
    field: 'isVirtual',
    required: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.POINT_VIRTUAL, 'number'),
    },
  },
  {
    label: '邮寄类型',
    field: 'logisticsType',
    required: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.POINT_POST, 'number'),
    },
  },
  {
    label: '邮费',
    field: 'postage',
    required: true,
    component: 'Input',
  },
  {
    label: '物流ID',
    field: 'logisticsId',
    component: 'Input',
  },
  {
    label: '物流简称',
    field: 'logisticsEnname',
    component: 'Input',
  },
  {
    label: '物流名称',
    field: 'logisticsName',
    component: 'Input',
  },
  {
    label: '物流运单号',
    field: 'logisticsNum',
    component: 'Input',
  },
  {
    label: '发货时间',
    field: 'deliveryTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '订单完成时间',
    field: 'completeTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '订单过期时间',
    field: 'expiredTime',
    required: true,
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
]

// 发货表单配置
export const deliveryFormSchema: FormSchema[] = [
  {
    field: 'orderNo',
    label: '订单编号',
    component: 'Input',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'productInfo',
    label: '商品规格名称',
    component: 'Input',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'orderQuantity',
    label: '商品数量',
    component: 'Input',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'orderPoints',
    label: '订单积分',
    component: 'Input',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'postage',
    label: '快递费',
    component: 'Input',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'totalAmount',
    label: '订单总额',
    component: 'Input',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'consignee',
    label: '收货人',
    component: 'Input',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'phone',
    label: '手机号码',
    component: 'Input',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'remark',
    label: '交易摘要',
    component: 'InputTextArea',
    componentProps: {
      disabled: true,
      rows: 1,
      placeholder: '请输入内容',
    },
  },
  {
    field: 'receiverAddress',
    label: '收货地址',
    component: 'InputTextArea',
    componentProps: {
      disabled: true,
      rows: 1,
    },
  },
  {
    field: 'logisticsId',
    label: '快递公司',
    component: 'Select',
    required: true,
    componentProps: {
      options: [],
      placeholder: '请选择快递公司',
    },
  },
  {
    field: 'logisticsNum',
    label: '快递单号',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '快递单号',
    },
  },
]

export const updateFormSchema: FormSchema[] = [
  {
    label: '编号',
    field: 'id',
    show: false,
    component: 'Input',
  },
  {
    label: '订单编号',
    field: 'orderNo',
    show: false,
    component: 'Input',
  },
  {
    label: '昵称',
    field: 'nickname',
    show: false,
    component: 'Input',
  },
  {
    label: '会员ID',
    field: 'memberId',
    // required: true,
    component: 'Input',
  },
  {
    label: '支付积分值',
    field: 'orderPoints',
    show: false,
    component: 'Input',
  },
  {
    label: '收货人',
    field: 'consignee',
    component: 'Input',
  },
  {
    label: '收货人手机号码',
    field: 'phone',
    component: 'Input',
  },
  {
    label: '省份',
    field: 'province',
    component: 'Input',
  },
  {
    label: '省份编码',
    field: 'provinceCode',
    component: 'Input',
  },
  {
    label: '城市',
    field: 'city',
    component: 'Input',
  },
  {
    label: '城市编码',
    field: 'cityCode',
    component: 'Input',
  },
  {
    label: '区县',
    field: 'district',
    component: 'Input',
  },
  {
    label: '区县编码',
    field: 'districtCode',
    component: 'Input',
  },
  {
    label: '详细地址',
    field: 'address',
    component: 'Input',
  },
  {
    label: '订单商品数量',
    field: 'orderQuantity',
    show: false,
    component: 'Input',
  },
  {
    label: '订单状态',
    field: 'orderStatus',
    required: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.TRADE_ORDER_STATUS, 'number'),
    },
  },
  {
    label: '订单金额',
    field: 'totalAmount',
    required: true,
    component: 'Input',
  },
  {
    label: '支付金额',
    field: 'payAmount',
    required: true,
    component: 'Input',
  },
  {
    label: '支付状态',
    field: 'payStatus',
    required: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.PAY_ORDER_STATUS, 'number'),
    },
  },
  {
    label: '下单时间',
    field: 'orderTime',
    show: false,
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '支付时间',
    field: 'payTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '退款状态',
    field: 'refundStatus',
    required: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.POINT_REFUND_STATUS, 'number'),
    },
  },
  {
    label: '退款时间',
    field: 'refundTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '取消时间',
    field: 'cancelTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '取消原因',
    field: 'cancelReason',
    component: 'Input',
  },
  {
    label: '订单备注',
    field: 'remark',
    component: 'Input',
  },
  {
    label: '是否虚拟',
    field: 'isVirtual',
    required: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.POINT_VIRTUAL, 'number'),
    },
  },
  {
    label: '邮寄类型',
    field: 'logisticsType',
    required: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.POINT_POST, 'number'),
    },
  },
  {
    label: '邮费',
    field: 'postage',
    required: true,
    component: 'Input',
  },
  {
    label: '物流ID',
    field: 'logisticsId',
    component: 'Input',
  },
  {
    label: '物流简称',
    field: 'logisticsEnname',
    component: 'Input',
  },
  {
    label: '物流名称',
    field: 'logisticsName',
    component: 'Input',
  },
  {
    label: '物流运单号',
    field: 'logisticsNum',
    component: 'Input',
  },
  {
    label: '发货时间',
    field: 'deliveryTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '订单过期时间',
    field: 'expiredTime',
    required: true,
    component: 'DatePicker',
    show: false,
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '订单完成时间',
    field: 'completeTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '交易关闭时间',
    field: 'closedTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
]
