# 网站标题
VITE_GLOB_APP_TITLE = 找车友-车主社群运营平台

# 网站ico
VITE_GLOB_APP_FAVICON = /console/resource/carclub-saas/favicon.png

# 网站logo
VITE_GLOB_APP_LOGO = /console/resource/carclub-saas/logo.png

# 网站logo白色
VITE_GLOB_APP_LOGO_WHITE = /console/resource/carclub-saas/logo-white.png

# 网站登录页背景
VITE_GLOB_APP_LOGIN_BG = /console/resource/carclub-saas/login-bg.jpg

# 资源公共路径,需要以 / 开头和结尾
VITE_PUBLIC_PATH = /console/

# 如果接口地址匹配到，则会转发到http://localhost:3000，防止本地出现跨域问题
# 可以有多个，注意多个不能换行，否则代理将会失效
# VITE_PROXY=[["/api/admin-api","https://carclub-dev.xiangchaokeji.com/api/admin-api"],["/upload","https://carclub-dev.xiangchaokeji.com/api/admin-api/infra/file/upload"]]
VITE_PROXY=[["/api/admin-api","http://localhost:48080/api/admin-api"],["/upload","http://localhost:48080/api/admin-api/infra/file/upload"]]

# 是否删除Console.log
VITE_DROP_CONSOLE = false

# 打包是否输出gz｜br文件
# 可选: gzip | brotli | none
# 也可以有多个, 例如 ‘gzip’|'brotli',这样会同时生成 .gz和.br文件
VITE_BUILD_COMPRESS = 'gzip'

# 使用compress时是否删除源文件，默认false
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false

# 基础页面地址，例如 swagger 等页面
VITE_GLOB_BASE_URL = "https://carclub-dev.xiangchaokeji.com/api"

# 接口地址 可以由nginx做转发或者直接写实际地址
VITE_GLOB_API_URL = /api/admin-api

# 文件上传地址 可以由nginx做转发或者直接写实际地址
VITE_GLOB_UPLOAD_URL = /api/admin-api/infra/file/upload

# 接口地址前缀，有些系统所有接口地址都有前缀，可以在这里统一加，方便切换
VITE_GLOB_API_URL_PREFIX =

# 打包是否开启pwa功能
VITE_USE_PWA = false

# 百度统计
VITE_APP_BAIDU_CODE = eb21166668bf766b9d059a6fd1c10777
